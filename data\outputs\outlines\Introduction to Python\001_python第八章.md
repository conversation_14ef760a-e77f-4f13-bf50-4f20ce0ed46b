# Python 函数基础与应用

## 函数概述
### 函数是带名字的代码块，可以多次调用，避免重复编写相同任务的代码。
### 使用函数让程序编写、阅读、测试和修复更容易。
### 函数可接受信息（实参），并根据需求完成特定任务。
### 函数可以直接显示信息或处理数据并返回结果。
### 函数可存储在模块中，保持主程序文件整洁。
### 良好的函数结构和命名有助于错误排查和维护。

## 函数定义与调用
### 函数定义使用 def 关键字，指定函数名和形参列表，括号必不可少。
### 函数体由缩进行构成，通常包含文档字符串来描述功能。
### 调用函数时，需用括号并传入必要的信息。
### 文档字符串用三个引号包裹，可生成自动化文档。
### 函数调用让 Python 执行定义的代码块。
### 无参数函数依然需用括号调用，参数可通过括号指定。

## 参数传递方式
### 位置实参要求实参顺序与形参一致，顺序错误会导致意外结果。
### 关键字实参用变量名指定值，顺序无关紧要且更清晰。
### 默认值可让部分参数在调用时省略，简化函数调用。
### 混合使用位置实参、关键字实参与默认值可实现多种等效调用方式。
### 必须保证实参数量与函数定义匹配，否则会出现错误信息。
### 函数定义时无默认值的形参应放前面，有默认值的放后面。

## 返回值与可选参数
### 使用 return 语句让函数返回结果到调用处，便于数据处理和复用。
### 可通过默认值让某些参数变为可选，简化函数调用。
### 可选参数通常放在形参列表末尾，且有默认值。
### 函数能返回字典、列表等结构，支持扩展存储更多信息。
### 可结合 if 语句灵活处理参数是否提供并返回不同结果。
### 返回值可被赋给变量，用于后续操作。

## 函数与控制结构结合
### 函数可与 while 循环结合，实现持续交互和动态输入。
### 通过添加退出条件，提升用户体验和程序健壮性。
### 可在循环中调用函数，简化重复任务的编码。
### 函数调用可嵌套于各种控制结构中，提升代码灵活性。
### 结合 input()、break 等语句实现交互式流程。
### 函数结合流程控制结构，易于扩展和维护。

## 列表参数处理
### 函数可接收列表参数，对列表批量处理如遍历和输出。
### 在函数内修改列表会影响原列表，适合高效数据处理。
### 可通过切片传递列表副本，避免影响原始数据。
### 处理批量数据时使用函数能提升代码复用和清晰度。
### 用两个函数分别处理不同任务，保持每个函数职责单一。
### 描述性函数名和结构有助于扩展和理解代码。

## 任意参数传递
### 用 *args 语法收集任意数量的位置实参，封装为元组。
### 可结合位置实参与 *args，先处理固定参数再收集剩余参数。
### 用 **kwargs 语法收集任意数量的关键字实参，封装为字典。
### 适用于参数数量未知或需灵活扩展的场景，如用户信息和选项。
### 任意参数收集提升函数通用性和适应性。
### 函数定义时 *args 或 **kwargs 必须放在最后，保证参数匹配。

## 模块化管理
### 模块是包含函数的 .py 文件，可通过 import 语句导入使用。
### 可导入整个模块或指定函数，保持主程序简洁。
### as 关键字可为函数或模块指定别名，避免命名冲突或简化调用。
### 使用 from module import * 可导入全部函数，但易引发命名冲突。
### 推荐只导入需要的函数或使用点号调用，提升代码可读性。
### 模块化设计有利于团队协作和代码共享。

## 编码规范
### 函数名应描述性强，只用小写字母和下划线。
### 每个函数应有文档字符串，说明功能、参数和返回值。
### 关键字参数赋值时等号两边无空格，保持一致性。
### 行长度不超 79 字符，形参多时可分行书写并保持缩进。
### 相邻函数间用两个空行分隔，便于结构识别。
### 所有 import 语句应放在文件开头，便于管理依赖。

## 函数优势总结
### 函数支持一次编写、多次复用，减少重复劳动。
### 修改函数只需改一个代码块，所有调用处自动更新行为。
### 良好函数结构和命名让程序更易理解和维护。
### 函数易于单独测试和调试，提升程序可靠性。
### 编写简单、结构化函数是程序员实现高效开发的重要目标。
### 函数是后续学习类和更高级结构的基础。