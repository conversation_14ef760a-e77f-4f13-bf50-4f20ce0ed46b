# 函数基础与应用指南

## 函数概述与模块导入
### 函数是带名字的代码块，可通过调用多次执行相同任务，避免重复编写代码。
### 使用函数让程序编写、阅读、测试和修复更容易。
### 可以通过多种方式向函数传递信息，包括参数和返回值。
### 函数可存储在模块中，实现代码的分离和复用。
### 模块让主程序更整洁，并便于与他人共享代码。
### 通过 import 语句可以导入模块，使用其中的函数。

## 函数定义基础
### def 关键字用于定义函数，函数名和括号必不可少。
### 括号内可包含函数需要的信息（参数），无参数时括号也不能省略。
### 函数体通过缩进表示，紧跟函数定义后的字符串为文档字符串。
### 文档字符串用三个双引号括起，描述函数用途，可多行。
### 函数调用时写函数名和括号，括号中传递必要的信息。
### 没有参数的函数调用时括号留空，执行定义的代码块。

## 形参与实参
### 形参是函数定义时用于接收外部信息的变量。
### 实参是在函数调用时传递给函数的实际值。
### 实参与形参的数量和顺序必须匹配，否则会导致错误。
### 形参和实参有时容易混淆，需注意二者的本质区别。
### 可以多次调用函数，每次传入不同实参实现不同输出。

## 实参传递方式
### 位置实参要求实参与形参顺序一致，顺序错误会导致意外结果。
### 关键字实参通过参数名指定值，顺序无关，避免混淆。
### 可以给形参指定默认值，调用时可省略，简化调用。
### 默认值参数应放在没有默认值参数之后。
### 实参传递方式可以混合使用，选择最适合的方式。
### 实参数量不匹配时，Python 会抛出错误并给出具体信息。

## 函数返回值
### 使用 return 语句将结果返回到函数调用处。
### 返回值可赋给变量，用于后续处理。
### 可以让某些参数变成可选参数，通过默认值实现。
### 函数可返回复杂数据类型，如字典或列表。
### 可选参数应在参数列表最后，并检查是否提供。
### 返回值让主程序更简洁，繁重工作交给函数处理。

## 函数与控制结构结合
### 函数可与 while 循环结合，重复请求输入或处理数据。
### 应在循环中提供退出条件，避免死循环。
### 可以在循环中调用函数生成动态输出。
### 函数体内可使用条件判断灵活处理不同情形。
### 函数结构清晰有助于分离逻辑和交互部分。

## 列表作为函数参数
### 函数参数可接收列表，实现批量处理和遍历。
### 在函数中可修改传入的列表，这会影响原列表。
### 使用函数组织处理过程，提高代码条理性和复用性。
### 可将处理逻辑和展示逻辑分成多个函数。
### 若不想修改原列表，可传递列表副本给函数。
### 函数操作列表时应注意性能和数据一致性。

## 可变数量参数
### 使用 *args 语法收集任意数量的位置实参，形成元组。
### 可用循环遍历收集到的实参，处理不同数量的数据。
### 结合普通参数和 *args 时，*args 必须放在参数列表最后。
### 使用 **kwargs 语法收集任意数量的关键字实参，形成字典。
### 可以混合使用位置参数、*args 和 **kwargs，满足多样需求。
### 这种机制常用于不确定参数数量的场景，如动态配置或扩展。

## 模块化管理
### 可将函数存储在独立的 .py 文件中，称为模块。
### 使用 import 语句导入整个模块，调用时用模块名.函数名。
### 可用 from ... import ... 语法只导入特定函数，调用时直接用函数名。
### as 关键字可为导入的函数或模块指定别名，避免命名冲突。
### from ... import * 可导入模块中所有函数，但不建议用于大型项目。
### 推荐只导入需要的函数或整个模块，保持代码清晰可读。

## 编码规范
### 函数和模块名应使用小写字母和下划线，保证描述性和可读性。
### 每个函数都应包含文档字符串，简明描述功能和参数。
### 给形参指定默认值时等号两边不加空格。
### 代码行长度不超过 79 个字符，参数多时可多行排列。
### 相邻函数之间用两个空行分隔，便于区分。
### import 语句应放在文件开头，便于管理依赖。

## 函数优势总结
### 编写函数后可多次复用，调用方式简洁高效。
### 修改函数只需修改一个地方，所有调用自动获得新行为。
### 函数名应概述其用途，阅读函数调用有助于理解程序结构。
### 函数便于单独测试和调试，提高代码可靠性。
### 合理使用函数让程序易于扩展和维护，符合编程最佳实践。